require 'sidekiq/web'

Rails.application.routes.draw do
  authenticate :user, ->(user) { user.admin? } do
    mount Sidekiq::Web => '/sidekiq'
  end
  get "pending_participants/index"
  get "site_control_incidents/new"
  get "site_control_incidents/create"
  devise_for :users, controllers: {
    sessions: 'users/sessions',
    invitations: 'users/invitations'
  }
  root to: "sites#index"
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Route pour arrêter l'incarnation (accessible depuis n'importe quelle page)
  post '/stop_impersonating', to: 'admin/users#stop_impersonating', as: :stop_impersonating

  # Espace administration
  namespace :admin do
    get '/', to: 'dashboard#index', as: :dashboard
    get '/healthcheck', to: 'healthcheck#index', as: :healthcheck
    get '/healthcheck/refresh', to: 'healthcheck#refresh', as: :refresh_admin_healthcheck
    resources :users, only: [:index, :new, :create, :destroy] do
      member do
        post :impersonate
      end
      collection do
        post :stop_impersonating
      end
      resources :user_sites, only: [:index, :create, :destroy] do
        member do
          post :add_role
          delete 'remove_role/:user_role_site_id', to: 'user_sites#remove_role', as: :remove_role
        end
      end
    end
    resources :sites, only: [:index, :new, :create, :destroy]
    resources :companies, only: [:index, :new, :create, :edit, :update, :destroy] do
      member do
        get :edit_logo
      end
    end
    resources :uploads, only: [:index] do
      member do
        get :download_files
      end
    end
    resources :documents, only: [:edit, :update] do
      member do
        get :serve
      end
    end
    resources :user_role_sites, only: [:destroy], as: 'role_sites'
    resources :site_configuration_templates
    resources :upload_logs, only: [:index]
  end

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # Render dynamic PWA files from app/views/pwa/*
  get "service-worker" => "rails/pwa#service_worker", as: :pwa_service_worker
  get "manifest" => "rails/pwa#manifest", as: :pwa_manifest

  resources :documents, only: [:index, :show, :create, :update, :destroy, :edit] do
    member do
      get :serve
      get :edit_documentable
      get :edit
      get :document_history
      get :new_participant
      get :documentable_details
    end
    collection do
      patch :archive
    end
  end

  resources :sites do
    get 'update_validity_status', to: 'sites#update_validity_status', as: 'update_validity_status'
    resources :site_settings, only: [:index, :update]
    get 'site_settings/lots', to: 'site_settings#lots', as: 'site_settings_lots'
    resources :settings, only: [:index, :update]
    resources :features, only: [:index, :update]
    resources :guardian, only: [:index, :new, :create, :edit, :update, :destroy] do
      member do
        patch :update_status
      end
    end
    resource :access_control, only: [:show], controller: 'access_control' do
      patch :update_interval
      post :sync
    end
    resources :vetting_processes, only: [:index, :show, :edit, :update] do
      collection do
        patch :bulk_approve
        patch :bulk_reject
      end
    end
    resources :documents, only: [:destroy] do
      collection do
        post :create_batch
      end
      member do
        get :extracted_fields
      end
    end
    resources :document_types, only: [:index, :new, :create, :edit, :update, :destroy]
    resources :pending_participants, only: [:index, :new, :create, :edit, :update, :destroy] do
      member do
        patch :update_image
        patch :convert
      end
      collection do
        delete :destroy
      end
    end
    resources :uploads, only: [:index, :show, :create, :update, :destroy] do
      collection do
        delete :destroy
      end
      patch :bulk_validate, on: :collection
    end
    resources :reporting, only: [:index]
    resources :sanctions, only: [:index, :new, :create]
    resources :safety_sessions, only: [:index, :show, :new, :create]
    resources :site_equipments, only: [:index]
    resources :equipment_donations, only: [:new, :create]
    resources :conditions
    resource :site_detail, only: [:show, :edit, :update]
    resources :condition_groups do
      resources :condition_group_mappings, only: [:new, :create, :destroy]
    end
    resources :site_controls, only: [:index, :show, :new, :create, :update] do
      resources :site_control_incidents, only: [:new, :create]
    end
    resources :participants do
      collection do
        get :search
        delete :destroy
      end
      member do
        resources :bypasses, model: 'participant'
        patch :update_image
        get 'documents', to: 'documents#index', model: 'participant'
        post :sync_with_external_service
      end
    end
    resources :interim_accesses, only: [:create]
    resources :companies, only: %i[new create show] do
      collection do
        get :search_company
      end
      member do
        get :participants
      end
    end
    resources :locker_damages, only: [:new, :create]
    resources :locker_participants, only: [:index] do
      collection do
        post :assign
      end
    end
    resources :lockers do
      patch :empty, on: :member
      resources :locker_participants, only: [:create, :destroy] do
        patch :unassign, on: :member
      end
      collection do
        resources :lockers_damages
      end
      collection do
        post :batch_create
      end
    end
    resources :assignments do
      collection do
        get :search_company
        get :lots
      end
      member do
        get :user_sites
        get 'documents', to: 'documents#index', model: 'assignment'
        get 'participants', to: 'assignments#participants'
        get 'manage_lots', to: 'assignments#manage_lots'
        patch 'update_lots', to: 'assignments#update_lots'
        get :edit_start_date
      end
      get :hierarchy, on: :collection
    end
    resources :presences do
      collection do
        get :chart_data
      end
    end
    # resources :contacts
    resources :document_types, only: [:index] do
      get :fields, on: :member
    end
    resources :site_roles, only: [:index, :new]
    resources :participant_role_sites, only: %i[create]
    resources :assignment_role_sites, only: [:create]
    resources :role_contexts do
      resources :site_role_condition_groups, only: [:new, :create, :destroy]
    end
    resources :assignment_role_sites do
      resources :site_role_condition_groups, only: [:new, :create, :destroy]
    end
    resource :construction_board, only: [:show, :edit, :update] do
      resources :images, only: [:create, :destroy], controller: 'construction_board_images'
      get 'images/:id', to: 'construction_board_images#show', as: :image
      delete 'images/:id', to: 'construction_board_images#destroy'
      member do
        get :download_qr_code
      end
    end
    resources :lots, except: [:index, :show]
    resources :stakeholders, only: [:edit, :update, :new, :create]
    resources :mashe_tag, only: [:index, :new, :create] do
      collection do
        get :search_participants
      end
    end
    resources :document_fields do
      collection do
        patch :update_multiple
      end
    end
    resources :site_meetings, only: %i[index new edit update create show destroy] do
      resources :attendees, only: %i[new create update] do
        member do
          patch :update_access
        end
      end
    end
    resources :user_sites, only: %i[index new create destroy]
    get 'user_sites/:user_site_id/resend_invitation', to: 'user_sites#resend_invitation', as: :resend_invitation
    resources :user_role_sites, only: %i[index new create edit update] do
      member do
        get :available_roles_by_type
      end
    end
    resources :user_permission_sites, only: %i[edit update]
    resources :user_roles do
      member do
        get :edit_duplicate
        post :duplicate
      end
    end
    resources :default_user_assignment_roles, except: %i[index] do
      member do
        get :edit_duplicate
      end
      collection do
        post :create_duplicate
      end
    end
    resources :visual_badge_requests, only: %i[index create] do
      collection do
        get :requested_badges
        delete :destroy
        post :display_badges
        post :send_badges_by_email
      end
    end
    resources :key_sets, only: %i[index new create edit update show] do
      member do
        post :return_to_origin
        post :return_from_origin
        post :lost_key_set
        post :return_from_participant
      end
      resources :key_movements, only: %i[new create update]
    end
    resources :presence_movements, only: [:index] do
      collection do
        post :sync
      end
    end
    post 'mailer_interface/invalid_participant_email/:participant_id', to: 'mailer_interface#invalid_participant_email', as: :invalid_participant_email
    get 'mailer_interface/select_user_show/:participant_id', to: 'mailer_interface#select_user_show', as: :select_user_show
    # Routes pour la signature de documents
    get 'document_signatures/new', to: 'document_signatures#new', as: 'document_signature'
    post 'document_signatures/:id', to: 'document_signatures#create'
  end

  resources :companies, only: %i[update] do
    resources :people, only: %i[update]
    member do
      patch :update_image
    end
  end
  resources :attendees, only: %i[destroy edit show] do
    member do
      get :send_access_code
      post :verify_email
    end
  end

  # resources :gdpr, only: [:index]
  # resources :free_fields, only: [:edit, :update]
  # resources :gdpr_requests, only: [:edit, :update, :new, :create] do
  #   collection do
  #     get 'new_suppression', to: 'gdpr_requests#new_suppression'
  #     post 'create_suppression', to: 'gdpr_requests#create_suppression'
  #   end
  # end

  scope :users do
    get 'otp_verification/new', to: 'users/otp_verification#new', as: :users_otp_verification_new
    post 'verify_otp', to: 'users/otp_verification#create', as: :users_verify_otp
    post 'resend_otp', to: 'users/otp_verification#resend', as: :users_resend_otp
  end

  namespace :api do
    resources :participant_searches, only: [:index]
    resources :company_searches, only: [:index]
    resources :documentable_searches, only: [:index]
    resources :participant_role_sites do
      get :by_assignment, on: :collection
    end
    post 'webhooks/echo', to: 'echo_webhooks#create'
  end

  # Routes pour les fusions
  resources :merges, only: [:new, :create]

  resources :document_thumbnails, only: [:show]
  resources :document_associations, only: [:create, :destroy]

  # Route pour la création de participant depuis un document
  post 'documents/:id/create_participant', to: 'documents#create_participant', as: :create_participant_from_document

  # Route pour la prévisualisation PDF (hors namespace site car indépendante)
  get 'document_signatures/preview', to: 'document_signatures#preview'

  # Route pour les demandes de support
  resources :support_requests, only: [:create]

  # Routes pour le profil utilisateur
  resource :profile, only: [:edit, :update], controller: 'profiles'

end
