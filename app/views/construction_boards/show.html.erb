<div id="flash-messages"></div>

<div class="page-banner">
  <div class="page-banner__left">
    <div class="stat-item dashboard-card">
      <span class="stat-number"><%= @presenter.stakeholders_count %></span>
      <span class="stat-label">Parties prenantes</span>
    </div>
  </div>
  <div class="page-banner__right">
    <div class="page-banner__action" 
         data-controller="construction-board-image" 
         data-construction-board-image-csrf-token-value="<%= form_authenticity_token %>">
      <%= form_with(model: [@site, @construction_board], 
                    url: site_construction_board_path(@site, @construction_board), 
                    local: true, 
                    multipart: true, 
                    method: :patch,
                    data: { construction_board_image_target: "form" }) do |f| %>
        <%= f.file_field :images, 
                         multiple: true, 
                         class: 'd-none', 
                         accept: 'image/*',
                         data: { 
                           construction_board_image_target: "fileInput",
                           action: "change->construction-board-image#handleFileChange"
                         } %>
        <a class="" 
           href="#" 
           data-action="click->construction-board-image#triggerFileInput"
           data-construction-board-image-target="button">
          <i class="fas fa-image action-icon"></i>
          <span class="action-text">Ajouter</span>
        </a>
      <% end %>
    </div>

    <div class="page-banner__action">
      <%= link_to new_site_stakeholder_path(@site), data: { turbo_stream: true } do %>
        <i class="fas fa-user-plus action-icon"></i>
        <span class="action-text">Partie prenante</span>
      <% end %>
    </div>

    <div class="page-banner__action">
      <%= link_to download_qr_code_site_construction_board_path(@site, @construction_board), data: { turbo: false } do %>
        <i class="fas fa-qrcode action-icon"></i>
        <span class="action-text">QR Code</span>
      <% end %>
    </div>

    <div class="page-banner__action">
      <%= link_to "#{ENV['MASHE_BOARDS_URL']}/boards/#{@construction_board.uuid}", target: "_blank" do %>
        <i class="fas fa-eye action-icon"></i>
        <span class="action-text">Voir le panneau</span>
      <% end %>
    </div>
  </div>
</div>

<div class="row">
  <div class="col-lg-6">
    <%= render partial: "site_information", locals: { presenter: @presenter } %>
  </div>
  <div class="col-lg-6">
    <%= render partial: "images_carousel", locals: { presenter: @presenter, site: @site } %>
  </div>
</div>

<%= render partial: "stakeholders/stakeholders", locals: { stakeholders: @site.stakeholders, site: @site } %>
