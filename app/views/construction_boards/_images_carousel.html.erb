<div class="dashboard-card" style="margin-top: 20px;">
  <h2 class="data-table-title">Autres images (<%= presenter.images_count %> images)</h2>
  <div class="carousel-container">
    <% if presenter.has_images? %>
      <div class="carousel-wrapper" data-controller="construction-board-carousel">
        <div id="constructionBoardCarousel" class="carousel slide" data-bs-ride="false" data-construction-board-carousel-target="carousel">
          <div class="carousel-inner">
            <% presenter.construction_board.images.each_with_index do |image, index| %>
              <div class="carousel-item <%= 'active' if index.zero? %>">
                <%= image_tag image, alt: "Image #{index + 1}" %>
                <div class="carousel-actions">
                  <%= button_to site_construction_board_image_path(site, image),
                              method: :delete,
                              data: { turbo_confirm: "Êtes-vous sûr de vouloir supprimer cette image ?" },
                              class: "btn btn-danger btn-sm" do %>
                    <i class="fas fa-trash action-icon"></i>
                    <span>Supprimer</span>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
          
          <% if presenter.images_count > 1 %>
            <button type="button" class="carousel-button left" data-bs-target="#constructionBoardCarousel" data-bs-slide="prev">
              <i class="fa-solid fa-chevron-left"></i>
            </button>
            
            <button type="button" class="carousel-button right" data-bs-target="#constructionBoardCarousel" data-bs-slide="next">
              <i class="fa-solid fa-chevron-right"></i>
            </button>
          <% end %>
        </div>
      </div>
    <% else %>
      <div class="empty-carousel">
        <i class="fas fa-images"></i>
        <p>Aucune image</p>
      </div>
    <% end %>
  </div>
</div> 