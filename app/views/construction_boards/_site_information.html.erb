<div class="dashboard-card site-profile-card">
  <div class="card-header">
    <h2 class="data-table-title">Informations du site</h2>
    <%= link_to edit_site_site_detail_path(presenter.site_detail.site, redirect_to: 'construction_board'), 
                class: 'edit-button', 
                data: { turbo_stream: true } do %>
      <i class="fas fa-edit edit-button-icon"></i>
    <% end %>
  </div>
  
  <div class="site-stats">
    <div class="stat-item">
      <span class="stat-number"><%= presenter.floor_area_display %></span>
      <span class="stat-label">Surface</span>
    </div>

    <div class="stat-item">
      <span class="stat-number"><%= presenter.permit_number || "N/A" %></span>
      <span class="stat-label">Permis de construire</span>
    </div>

    <div class="stat-item">
      <span class="stat-number"><%= presenter.formatted_permit_date || "N/A" %></span>
      <span class="stat-label">Date du permis</span>
    </div>
  </div>

  <div class="site-profile-content">
    <div class="site-profile-columns">
      <div class="site-profile-image-column">
        <% if presenter.has_site_image? %>
          <%= image_tag presenter.site_detail.site_image, class: "site-profile-image" %>
        <% else %>
          <div class="empty-profile-image">
            <i class="fas fa-building"></i>
            <p>Aucune image</p>
          </div>
        <% end %>
      </div>

      <div class="site-profile-info-column">
        <div class="details-list">
          <div class="detail-item">
            <span class="detail-label">Nom du site:</span>
            <span class="detail-value"><%= presenter.site_name %></span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Nom court:</span>
            <span class="detail-value"><%= presenter.site_short_name %></span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Entreprise:</span>
            <span class="detail-value"><%= presenter.company_name %></span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Adresse:</span>
            <span class="detail-value"><%= presenter.site_address %></span>
          </div>
          <% if presenter.description.present? %>
            <div class="detail-item">
              <span class="detail-label">Description:</span>
              <span class="detail-value"><%= presenter.description %></span>
            </div>
          <% end %>
          <% if presenter.base_email.present? %>
            <div class="detail-item">
              <span class="detail-label">Email de base:</span>
              <span class="detail-value">
                <a href="mailto:<%= presenter.base_email %>"><%= presenter.base_email %></a>
              </span>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div> 