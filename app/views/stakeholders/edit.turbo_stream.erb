<%= turbo_stream.update "modal-container" do %>
  <div class="mashe-modal" data-controller="mashe-modal">
    <div class="mashe-modal__overlay" data-action="click->mashe-modal#close"></div>
    <div class="mashe-modal__content">
      <button class="mashe-modal__close" data-action="click->mashe-modal#close">&times;</button>
      <div class="mashe-modal__body stakeholder-form">
        <%= form_with model: [@site, @stakeholder], data: { turbo: true }, multipart: true do |f| %>
          <%= f.hidden_field :site_id, value: @stakeholder.site_id %>
          <div class="modal-header">
            <h5 class="modal-title">Modifier la partie prenante</h5>
          </div>

          <div class="modal-body">
            <div class="row mb-3">
              <div class="col-md-6">
                <%= f.label :name, "Nom", class: "form-label" %>
                <%= f.text_field :name, class: "form-control" %>
                <% if @stakeholder.errors[:name].any? %>
                  <div class="text-danger"><%= @stakeholder.errors[:name].join(", ") %></div>
                <% end %>
              </div>
              <div class="col-md-6">
                <%= f.label :role, "Rôle", class: "form-label" %>
                <%= f.text_field :role, class: "form-control" %>
                <% if @stakeholder.errors[:role].any? %>
                  <div class="text-danger"><%= @stakeholder.errors[:role].join(", ") %></div>
                <% end %>
              </div>
            </div>

            <div class="form-group mb-3">
              <%= f.label :address, "Adresse", class: "form-label" %>
              <%= f.text_field :address, class: "form-control" %>
              <% if @stakeholder.errors[:address].any? %>
                <div class="text-danger"><%= @stakeholder.errors[:address].join(", ") %></div>
              <% end %>
            </div>

            <div class="form-group mb-3" data-controller="stakeholder-logo">
              <%= f.label :logo, "Logo", class: "form-label" %>
              <% if @stakeholder.logo.attached? %>
                <div class="current-logo mb-2" data-stakeholder-logo-target="currentLogo">
                  <small class="form-text text-muted">Logo actuel:</small>
                  <div class="d-flex align-items-center mt-1">
                    <%= image_tag @stakeholder.logo, class: "stakeholder-logo-preview me-2", style: "width: 50px; height: 50px; object-fit: cover; border-radius: 4px;" %>
                    <span class="text-muted"><%= @stakeholder.logo.filename %></span>
                  </div>
                </div>
              <% end %>
              <%= f.file_field :logo, 
                               class: "form-control", 
                               accept: "image/*",
                               data: { 
                                 stakeholder_logo_target: "fileInput",
                                 action: "change->stakeholder-logo#handleFileChange"
                               } %>
              <div data-stakeholder-logo-target="preview" style="display: none;" class="mt-2">
                <small class="form-text text-muted">Nouveau logo:</small>
              </div>
              <small class="form-text text-muted">
                <% if @stakeholder.logo.attached? %>
                  Sélectionnez un nouveau fichier pour remplacer le logo actuel
                <% else %>
                  Formats acceptés: JPG, PNG, GIF (max 5MB)
                <% end %>
              </small>
              <% if @stakeholder.errors[:logo].any? %>
                <div class="text-danger"><%= @stakeholder.errors[:logo].join(", ") %></div>
              <% end %>
            </div>

            <div class="other-infos-section" data-controller="dynamic-fields">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">Informations complémentaires</h6>
                <%= link_to "#", class: "add-stakeholder-button", data: { action: "click->dynamic-fields#addField" } do %>
                  <i class="fas fa-plus action-icon"></i>
                  <span class="action-text">Ajouter</span>
                <% end %>
              </div>

              <div class="other-infos-fields">
                <% @stakeholder.other_infos.each do |key, value| %>
                  <div class="row mb-3 other-info-field">
                    <div class="col-md-5">
                      <%= text_field_tag "stakeholder[other_infos_keys][]", key, class: "form-control", placeholder: "Nom du champ" %>
                    </div>
                    <div class="col-md-5">
                      <%= text_field_tag "stakeholder[other_infos_values][]", value, class: "form-control", placeholder: "Valeur" %>
                    </div>
                    <div class="col-md-2 d-flex justify-content-center">
                      <%= link_to "#", class: "delete-stakeholder-field", data: { action: "click->dynamic-fields#removeField" } do %>
                        <i class="fas fa-trash action-icon"></i>
                      <% end %>
                    </div>
                  </div>
                <% end %>
                <% if @stakeholder.errors[:other_infos].any? %>
                  <div class="text-danger"><%= @stakeholder.errors[:other_infos].join(", ") %></div>
                <% end %>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <%= f.submit "Enregistrer", class: "mashe-button mashe-button--primary" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
<% end %> 