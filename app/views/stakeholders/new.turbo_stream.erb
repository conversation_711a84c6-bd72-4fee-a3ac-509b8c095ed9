<%= turbo_stream.update "modal-container" do %>
  <div class="mashe-modal" data-controller="mashe-modal">
    <div class="mashe-modal__overlay" data-action="click->mashe-modal#close"></div>
    <div class="mashe-modal__content">
      <button class="mashe-modal__close" data-action="click->mashe-modal#close">&times;</button>
      <div class="mashe-modal__body stakeholder-form">
        <%= form_with model: [@site, @stakeholder], data: { turbo: true }, multipart: true do |f| %>
          <div class="modal-header">
            <h5 class="modal-title">Ajouter une partie prenante</h5>
          </div>

          <div class="modal-body">
            <div class="row mb-3">
              <div class="col-md-6">
                <%= f.label :name, "Nom", class: "form-label" %>
                <%= f.text_field :name, class: "form-control", required: true %>
                <% if @stakeholder.errors[:name].any? %>
                  <div class="text-danger"><%= @stakeholder.errors[:name].join(", ") %></div>
                <% end %>
              </div>
              <div class="col-md-6">
                <%= f.label :role, "Rôle", class: "form-label" %>
                <%= f.text_field :role, class: "form-control", required: true %>
                <% if @stakeholder.errors[:role].any? %>
                  <div class="text-danger"><%= @stakeholder.errors[:role].join(", ") %></div>
                <% end %>
              </div>
            </div>

            <div class="form-group mb-3">
              <%= f.label :address, "Adresse", class: "form-label" %>
              <%= f.text_field :address, class: "form-control" %>
              <% if @stakeholder.errors[:address].any? %>
                <div class="text-danger"><%= @stakeholder.errors[:address].join(", ") %></div>
              <% end %>
            </div>

            <div class="form-group mb-3" data-controller="stakeholder-logo">
              <%= f.label :logo, "Logo", class: "form-label" %>
              <%= f.file_field :logo, 
                               class: "form-control", 
                               accept: "image/*",
                               data: { 
                                 stakeholder_logo_target: "fileInput",
                                 action: "change->stakeholder-logo#handleFileChange"
                               } %>
              <div data-stakeholder-logo-target="preview" style="display: none;" class="mt-2">
                <small class="form-text text-muted">Aperçu:</small>
              </div>
              <small class="form-text text-muted">Formats acceptés: JPG, PNG, GIF (max 5MB, optionnel)</small>
              <% if @stakeholder.errors[:logo].any? %>
                <div class="text-danger"><%= @stakeholder.errors[:logo].join(", ") %></div>
              <% end %>
            </div>

            <div class="other-infos-section" data-controller="dynamic-fields">
              <div class="d-flex justify-content-between align-items-center mb-3">
                <h6 class="mb-0">Informations complémentaires</h6>
                <%= link_to "#", class: "add-stakeholder-button", data: { action: "click->dynamic-fields#addField" } do %>
                  <i class="fas fa-plus action-icon"></i>
                  <span class="action-text">Ajouter</span>
                <% end %>
              </div>

              <div class="other-infos-fields">
                <!-- Les champs dynamiques seront ajoutés ici -->
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <%= f.submit "Créer", class: "mashe-button mashe-button--primary" %>
          </div>
        <% end %>
      </div>
    </div>
  </div>
<% end %> 