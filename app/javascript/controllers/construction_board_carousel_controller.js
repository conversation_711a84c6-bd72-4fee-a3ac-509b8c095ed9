import { Controller } from "@hotwired/stimulus"

/**
 * Contrôleur pour gérer le carousel d'images du panneau de chantier
 */
export default class extends Controller {
    static targets = ["carousel"]

    connect() {
        console.log("Construction board carousel controller connected")

        // Initialiser le carousel Bootstrap manuellement pour avoir plus de contrôle
        if (this.hasCarouselTarget) {
            // Désactiver l'auto-rotation
            this.carousel = new bootstrap.Carousel(this.carouselTarget, {
                interval: false,
                keyboard: true,
                pause: 'hover',
                wrap: true
            })
        }
    }

    disconnect() {
        // Nettoyer le carousel
        if (this.carousel) {
            this.carousel.dispose()
        }
    }

    // Méthodes pour contrôler le carousel si nécessaire
    next() {
        if (this.carousel) {
            this.carousel.next()
        }
    }

    prev() {
        if (this.carousel) {
            this.carousel.prev()
        }
    }
} 