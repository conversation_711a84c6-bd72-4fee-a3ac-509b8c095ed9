import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["fields"]

  addField(event) {
    event.preventDefault()
    const template = `
      <div class="row mb-3 other-info-field">
        <div class="col-md-5">
          <input type="text" name="stakeholder[other_infos_keys][]" class="form-control" placeholder="Nom du champ">
        </div>
        <div class="col-md-5">
          <input type="text" name="stakeholder[other_infos_values][]" class="form-control" placeholder="Valeur">
        </div>
        <div class="col-md-2 d-flex justify-content-center">
          <a href="#" class="delete-stakeholder-field" data-action="click->dynamic-fields#removeField">
            <i class="fas fa-trash action-icon"></i>
          </a>
        </div>
      </div>
    `
    this.element.querySelector('.other-infos-fields').insertAdjacentHTML('beforeend', template)
  }

  removeField(event) {
    event.preventDefault()
    event.target.closest('.other-info-field').remove()
  }
}
