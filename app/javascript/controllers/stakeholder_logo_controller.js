import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
    static targets = ["fileInput", "preview", "currentLogo"]

    connect() {
        console.log("Stakeholder logo controller connected")
    }

    handleFileChange(event) {
        const file = event.target.files[0]

        if (file) {
            // Validation du fichier
            if (!this.isValidImageFile(file)) {
                alert("Veuillez sélectionner un fichier image valide (JPG, PNG, GIF)")
                event.target.value = ""
                return
            }

            // Validation de la taille (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert("Le fichier est trop volumineux. Taille maximum: 5MB")
                event.target.value = ""
                return
            }

            // Afficher un aperçu si possible
            this.showPreview(file)
        }
    }

    isValidImageFile(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
        return validTypes.includes(file.type)
    }

    showPreview(file) {
        if (this.hasPreviewTarget) {
            const reader = new FileReader()

            reader.onload = (e) => {
                // Créer ou mettre à jour l'aperçu
                let previewImg = this.previewTarget.querySelector('img')
                if (!previewImg) {
                    previewImg = document.createElement('img')
                    previewImg.style.cssText = "width: 50px; height: 50px; object-fit: cover; border-radius: 4px; margin-right: 8px;"
                    this.previewTarget.appendChild(previewImg)
                }
                previewImg.src = e.target.result

                // Ajouter le nom du fichier
                let fileName = this.previewTarget.querySelector('.file-name')
                if (!fileName) {
                    fileName = document.createElement('span')
                    fileName.className = 'file-name text-muted'
                    this.previewTarget.appendChild(fileName)
                }
                fileName.textContent = file.name

                // Masquer le logo actuel s'il existe
                if (this.hasCurrentLogoTarget) {
                    this.currentLogoTarget.style.opacity = '0.5'
                }

                this.previewTarget.style.display = 'flex'
                this.previewTarget.style.alignItems = 'center'
            }

            reader.readAsDataURL(file)
        }
    }
} 