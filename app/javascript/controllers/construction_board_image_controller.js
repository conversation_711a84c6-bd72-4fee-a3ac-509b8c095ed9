import { Controller } from "@hotwired/stimulus"

/**
 * Contrôleur pour gérer l'upload d'images du panneau de chantier
 */
export default class extends Controller {
    static targets = ["fileInput", "form", "button"]
    static values = {
        csrfToken: String
    }

    connect() {
        console.log("Construction board image controller connected")
        // S'assurer que le token CSRF est disponible
        this.csrfTokenValue = this.csrfTokenValue || document.querySelector("meta[name='csrf-token']")?.content
    }

    // Méthode appelée quand on clique sur le bouton d'ajout d'image
    triggerFileInput(event) {
        event.preventDefault()
        console.log("Trigger file input clicked")

        if (this.hasFileInputTarget) {
            this.fileInputTarget.click()
        } else {
            console.error("File input target not found")
        }
    }

    // Méthode appelée quand un fichier est sélectionné
    handleFileChange(event) {
        const files = event.target.files
        console.log("Files selected:", files.length)

        if (files.length === 0) {
            console.log("No files selected")
            return
        }

        // Vérifier que tous les fichiers sont des images
        for (let file of files) {
            if (!file.type.startsWith("image/")) {
                console.error("Le fichier", file.name, "n'est pas une image")
                alert("Veuillez sélectionner uniquement des images")
                this.resetFileInput()
                return
            }
        }

        // Vérifier la taille des fichiers (max 10MB par fichier)
        for (let file of files) {
            if (file.size > 10 * 1024 * 1024) {
                console.error("Le fichier", file.name, "est trop volumineux")
                alert(`Le fichier "${file.name}" est trop volumineux. Taille maximum : 10MB`)
                this.resetFileInput()
                return
            }
        }

        console.log("All files are valid, submitting form")
        this.showLoading()
        this.submitForm()
    }

    // Soumettre le formulaire
    submitForm() {
        if (this.hasFormTarget) {
            console.log("Submitting form")
            this.formTarget.submit()
        } else {
            console.error("Form target not found")
            this.hideLoading()
        }
    }

    // Afficher l'indicateur de chargement
    showLoading() {
        if (this.hasButtonTarget) {
            this.buttonTarget.innerHTML = '<i class="fas fa-spinner fa-spin action-icon"></i><span class="action-text">Upload...</span>'
            this.buttonTarget.style.pointerEvents = 'none'
        }
    }

    // Masquer l'indicateur de chargement
    hideLoading() {
        if (this.hasButtonTarget) {
            this.buttonTarget.innerHTML = '<i class="fas fa-image action-icon"></i><span class="action-text">Ajouter</span>'
            this.buttonTarget.style.pointerEvents = 'auto'
        }
    }

    // Réinitialiser l'input file
    resetFileInput() {
        if (this.hasFileInputTarget) {
            this.fileInputTarget.value = ''
        }
    }

    // Gérer les erreurs de navigation (si l'utilisateur revient en arrière)
    disconnect() {
        this.hideLoading()
    }
} 