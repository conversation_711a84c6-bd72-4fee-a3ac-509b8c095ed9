class StakeholdersController < ApplicationController
  before_action :set_site
  before_action :set_stakeholder, only: [:edit, :update]

  # example from lot controller
  #
  #def edit
    # authorize @lot
  #
    # respond_to do |format|
      # format.html
      # format.turbo_stream { render layout: "modal" }
    # end
  # end
  def edit
    authorize @stakeholder

    respond_to do |format|
      format.html
      format.turbo_stream { render layout: "modal" }
    end
  end

  def update
    authorize @stakeholder
    if @stakeholder.update(stakeholder_params.except(:site_id))
      @stakeholder.reload
      @stakeholders = @site.stakeholders.reload
      respond_to do |format|
        format.turbo_stream
      end
    else
      Rails.logger.error(@stakeholder.errors.full_messages)
      render :edit, status: :unprocessable_entity
    end
  end

  def new
    @stakeholder = @site.construction_board.stakeholders.build
    authorize @stakeholder

    respond_to do |format|
      format.html
      format.turbo_stream { render layout: "modal" }
    end
  end

  def create
    @stakeholder = @site.construction_board.stakeholders.build(stakeholder_params)
    authorize @stakeholder

    if @stakeholder.save
      @stakeholders = @site.stakeholders.reload
      respond_to do |format|
        format.turbo_stream
      end
    else
      Rails.logger.error(@stakeholder.errors.full_messages)
      respond_to do |format|
        format.turbo_stream { render :new, status: :unprocessable_entity, layout: "modal" }
      end
    end
  end

  private

  def set_site
    @site = Site.find(params[:site_id])
  end

  def set_stakeholder
    @stakeholder = Stakeholder.find(params[:id])
  end

  def stakeholder_params
    params.require(:stakeholder).permit(:name, :role, :address, :logo, :site_id, other_infos_keys: [], other_infos_values: []).tap do |whitelisted|
      if whitelisted[:other_infos_keys].present? && whitelisted[:other_infos_values].present?
        whitelisted[:other_infos] = whitelisted[:other_infos_keys].zip(whitelisted[:other_infos_values]).to_h
        whitelisted.delete(:other_infos_keys)
        whitelisted.delete(:other_infos_values)
      end
    end
  end
end
