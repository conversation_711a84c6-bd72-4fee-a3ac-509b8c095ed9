class ConstructionBoardsController < ApplicationController
  before_action :set_page_infos
  before_action :set_construction_board_and_site, only: %i[show update]

  def show
    authorize @construction_board
    @presenter = ConstructionBoardPresenter.new(@construction_board, @site)
    policy_scope(ConstructionBoard)
  end

  def update
    authorize @construction_board

    if params[:construction_board][:images].present?
      image_service = ConstructionBoardImageService.new(@construction_board)

      if image_service.attach_images(params[:construction_board][:images])
        redirect_to site_construction_board_path(@site), notice: 'Images ajoutées avec succès.'
      else
        redirect_to site_construction_board_path(@site), alert: 'Erreur lors de l\'ajout des images.'
      end
    elsif @construction_board.update(construction_board_params)
      redirect_to site_construction_board_path(@site), notice: 'Panneau de chantier mis à jour avec succès.'
    else
      flash.now[:alert] = 'Une erreur est survenue.'
      @presenter = ConstructionBoardPresenter.new(@construction_board, @site)
      render :show
    end
  end

  def new
    @construction_board = @site.build_construction_board
    authorize @construction_board
  end

  def create
    @construction_board = @site.build_construction_board(construction_board_params)
    authorize @construction_board

    if @construction_board.save
      redirect_to site_construction_boards_path(@site), notice: 'Panneau de chantier créé avec succès.'
    else
      render :new
    end
  end

  def download_qr_code
    @construction_board = @site.construction_board
    authorize @construction_board

    image_service = ConstructionBoardImageService.new(@construction_board)
    qr_png = image_service.generate_qr_code
    presenter = ConstructionBoardPresenter.new(@construction_board, @site)

    send_data qr_png.to_s,
              filename: presenter.qr_code_filename,
              type: 'image/png',
              disposition: 'attachment'
  end

  private

  def set_construction_board_and_site
    @construction_board, @site = ConstructionBoardRepository.find_with_site_and_stakeholders(@site.id)
  end

  def construction_board_params
    params.require(:construction_board).permit(:enabled, :other_infos, images: [])
  end

  def set_page_infos
    @meta_title = "#{@site.name} - Panneau de chantier"
    @page_title = "Panneau de chantier"
    @tab = "construction_board"
  end
end
