class SiteDetailsController < ApplicationController
  before_action :set_site
  before_action :set_page_infos

  def show
    @site_detail = @site.site_detail || @site.build_site_detail
    @lots = @site.lots.includes(:assignments)
    @stakeholders = @site.stakeholders
    @construction_board = @site.construction_board
    @topbar_tab = "details"
    authorize @site_detail
  end

  def edit
    @site_detail = @site.site_detail || @site.build_site_detail
    authorize @site_detail

    respond_to do |format|
      format.html
      format.turbo_stream { render layout: "modal" }
    end
  end

  def update
    @site_detail = @site.site_detail || @site.build_site_detail
    authorize @site_detail

    # Séparer les paramètres du site et du site_detail
    site_params = site_detail_params.slice(:name, :short_name, :address)
    detail_params = site_detail_params.except(:name, :short_name, :address, :redirect_to)

    success = true

    # Mettre à jour le site si nécessaire
    if site_params.any?
      success = @site.update(site_params)
    end

    # Mettre à jour le site_detail si la mise à jour du site a réussi
    if success && detail_params.any?
      success = @site_detail.update(detail_params)
    end

    if success
      if params[:site_detail][:redirect_to] == 'construction_board'
        redirect_to site_construction_board_path(@site), notice: "Informations du site mises à jour avec succès"
      else
        redirect_to site_site_detail_path(@site), notice: "Informations du site mises à jour avec succès"
      end
    else
      # Collecter toutes les erreurs
      @errors = []
      @errors.concat(@site.errors.full_messages) if @site.errors.any?
      @errors.concat(@site_detail.errors.full_messages) if @site_detail.errors.any?

      render :edit, status: :unprocessable_entity
    end
  end

  private

  def site_detail_params
    params.require(:site_detail).permit(
      # Champs du site
      :name, :short_name, :address,
      # Champs du site_detail
      :description, :floor_area, :permit_number, :permit_date,
      :site_image, :base_email, :redirect_to
    )
  end

  def set_site
    @site = Site.find(params[:site_id])
  end

  def set_page_infos
    @meta_title = "#{@site.name} - Détails du site"
    @page_title = "Détails du site"
    @tab = "settings"
  end
end
