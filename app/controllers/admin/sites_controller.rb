class Admin::SitesController < Admin::AdminController
  before_action :set_site, only: [:destroy]

  def index
    @sites = Site.includes(:company)
                 .order(created_at: :desc)
                 .page(params[:page])
                 .per(5)

    return unless params[:search].present?

    search_term = "%#{params[:search]}%"
    @sites = @sites.where("name ILIKE ? OR address ILIKE ?", search_term, search_term)
  end

  def new
    @new_site = Site.new
    @new_site.build_site_detail
    @new_site.feature_mappings.build
    @companies = Company.order(:name)
    @features = Feature.all.group_by(&:category)
    @site_configuration_templates = SiteConfigurationTemplate.order(:name)

    respond_to do |format|
      format.html
      format.turbo_stream
    end
  end

  def create
    @new_site = Site.new(site_params)

    # Gestion des feature_mappings
    if params[:feature_mappings].present?
      params[:feature_mappings].each do |feature_name, value|
        if value == "1"
          feature = Feature.find_by(name: feature_name)
          @new_site.feature_mappings.build(feature: feature, enabled: true) if feature
        end
      end
    end

    if @new_site.save
      # Créer l'affectation de l'entreprise principale

      # --- Configuration du site via le template ---
      template = SiteConfigurationTemplate.find(params[:site_configuration_template_id])
      template ||= SiteConfigurationTemplate.find_by(is_default: true)
      if template
        begin
          template.configure_site(@new_site)
          message = 'Configuration initiale des rôles appliquée.'
        rescue StandardError => e
          Rails.logger.error "Erreur lors de la configuration auto du site #{@new_site.id}: #{e.message}"
          message =
            "Le chantier a été créé, mais une erreur est survenue lors de l'application de la configuration des rôles par défaut. Veuillez vérifier la configuration manuellement."
        end
      else
        message =
          "Le chantier a été créé, mais aucun template de configuration de rôles n'a été trouvé. La configuration des rôles devra être faite manuellement."
      end
      @new_site.create_main_company_assignment
      # --- Fin de la configuration ---

      respond_to do |format|
        format.html { redirect_to admin_sites_path, notice: message }
        format.turbo_stream do
          @sites = Site.includes(:company).order(created_at: :desc).page(params[:page]).per(5)
        end
      end
    else
      @companies = Company.order(:name)
      @features = Feature.all.group_by(&:category)
      @selected_features = params[:feature_mappings]&.select { |_, value| value == "1" }&.keys || []

      respond_to do |format|
        format.html { render :new, status: :unprocessable_entity }
        format.turbo_stream { render :new, status: :unprocessable_entity }
      end
    end
  end

  def destroy
    ActiveRecord::Base.transaction do
      # Supprimer d'abord les dépendances qui pourraient causer des problèmes
      @site.user_sites.each do |user_site|
        user_site.user_role_sites.destroy_all if user_site.user_role_sites.any?
        user_site.assignments_referents.destroy_all if user_site.assignments_referents.any?
        user_site.destroy
      end

      # Supprimer les assignments et leurs dépendances
      @site.assignments.each do |assignment|
        assignment.participants.each do |participant|
          participant.destroy
        end
        assignment.destroy
      end

      # Supprimer les role_contexts liés aux assignment_role_sites
      @site.assignment_role_sites.each do |ars|
        ars.role_contexts.destroy_all if ars.role_contexts.any?
        ars.site_role_condition_groups.destroy_all if ars.site_role_condition_groups.any?
      end

      # Supprimer les role_contexts liés aux participant_role_sites
      @site.participant_role_sites.each do |prs|
        prs.role_contexts.destroy_all if prs.role_contexts.any?
        prs.site_role_condition_groups.destroy_all if prs.site_role_condition_groups.any?
      end

      # Maintenant on peut supprimer le site
      @site.destroy!
    end

    respond_to do |format|
      format.html { redirect_to admin_sites_path, notice: 'Le chantier a été supprimé avec succès.' }
      format.turbo_stream do
        @sites = Site.includes(:company).order(created_at: :desc).page(params[:page]).per(5)
      end
    end
  rescue StandardError => e
    Rails.logger.error "Erreur lors de la suppression du site #{@site.id}: #{e.message}"

    respond_to do |format|
      format.html { redirect_to admin_sites_path, alert: "Impossible de supprimer ce chantier: #{e.message}" }
      format.turbo_stream do
        render turbo_stream: turbo_stream.append('flashes-container', partial: 'shared/flashes',
                                                                      locals: { alert: "Impossible de supprimer ce chantier: #{e.message}" })
      end
    end
  end

  private

  def set_site
    @site = Site.find(params[:id])
  end

  def site_params
    params.require(:site).permit(
      :name, :short_name, :address, :company_id, :locker_plan,
      site_detail_attributes: %i[base_email skip_validations]
    )
  end
end
