class ConstructionBoardImagesController < ApplicationController
  before_action :set_construction_board
  before_action :authorize_construction_board

  def destroy
    image_service = ConstructionBoardImageService.new(@construction_board)

    if image_service.remove_image(params[:id])
      redirect_to site_construction_board_path(@site), notice: 'Image supprimée avec succès.'
    else
      redirect_to site_construction_board_path(@site), alert: 'Erreur lors de la suppression de l\'image.'
    end
  end

  private

  def set_construction_board
    @construction_board = @site.construction_board
  end

  def authorize_construction_board
    authorize @construction_board, :manage_images?
  end
end
