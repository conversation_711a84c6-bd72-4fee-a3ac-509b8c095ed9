# == Schema Information
#
# Table name: assignments
#
#  id                      :integer          not null, primary key
#  company_id              :integer          not null
#  site_id                 :integer          not null
#  assignment_parent_id    :integer
#  creator_type            :string
#  creator_id              :integer
#  assignment_role_site_id :integer
#  start_date              :date
#  is_valid                :boolean          default("false"), not null
#  valid_from              :date
#  valid_until             :date
#  pending                 :boolean          default("false"), not null
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#
# Indexes
#
#  index_assignments_on_assignment_parent_id     (assignment_parent_id)
#  index_assignments_on_assignment_role_site_id  (assignment_role_site_id)
#  index_assignments_on_company_id               (company_id)
#  index_assignments_on_creator                  (creator_type,creator_id)
#  index_assignments_on_site_id                  (site_id)
#

class Assignment < ApplicationRecord
  include AssignmentStatistics
  include DocumentAssociable
  include ValidityTrigger
  belongs_to :company
  accepts_nested_attributes_for :company

  trigger_validity_update_on_change_of :valid_from, :valid_until, :is_valid, target_finder: lambda(&:participants)
  trigger_validity_update_on_change_of :assignment_role_site_id, target_finder: lambda { |instance|
    [instance] + instance.participants
  }

  belongs_to :assignment_role_site
  belongs_to :site
  belongs_to :assignment_parent, class_name: "Assignment", optional: true
  belongs_to :creator, polymorphic: true, optional: true

  ########### Laisser Verifiable sous assignment_role ###########
  has_one :assignment_role, through: :assignment_role_site
  delegate :assignment_role_category, to: :assignment_role
  include Verifiable
  ###############################################################

  # has_one :assignment_role_category, through: :assignment_role

  has_many :assignment_children, class_name: "Assignment", foreign_key: :assignment_parent_id, dependent: :destroy
  has_many :assignments_referents, dependent: :destroy
  has_many :contacts, dependent: :destroy
  has_many :documents, as: :documentable, dependent: :destroy
  has_many :participants, dependent: :destroy
  has_many :pending_participants, dependent: :destroy
  has_many :presences, dependent: :destroy
  has_many :sanctions, dependent: :destroy
  has_many :assignment_interim_accesses, as: :accessible, class_name: 'InterimAccess', dependent: :destroy
  has_many :assignment_lots, dependent: :destroy
  has_many :lots, through: :assignment_lots

  has_many :equipment_donations, through: :participants, dependent: :destroy
  has_many :locker_participants, through: :participants, source: :locker_participant
  has_many :safety_session_signatures, through: :participants, source: :safety_session_signatures, dependent: :destroy
  has_many :safety_sessions, through: :safety_session_signatures, source: :safety_session
  has_many :user_sites, through: :assignments_referents
  has_many :referents, through: :user_sites, source: :user
  has_many :authorized_interim_companies, through: :assignment_interim_accesses, source: :interim_company

  delegate :name, to: :company, prefix: true
  alias name company_name
  delegate :name, to: :site, prefix: true
  delegate :name, to: :assignment_role_site, prefix: true

  validates :company_id, uniqueness: { scope: :site_id }
  validates :assignment_role_site_id, presence: true
  before_validation :set_default_assignment_role_site, on: :create
  has_one :assignment_role, through: :assignment_role_site
  include Verifiable

  scope :pending, -> { where(pending: true) }

  def set_default_assignment_role_site
    self.assignment_role_site_id ||= AssignmentRoleSite.find_by(
      site: site,
      assignment_role: AssignmentRole.find_by(name: site.get_setting("assignments", "default_role_name"))
    )&.id
  end

  def referents_names
    referents.map(&:full_name)
  end

  def participants_count
    participants.count
  end

  def valid_indicator
    is_valid? ? "Autorisé" : "Refusé"
  end

  def started?
    start_date.present? && start_date <= Date.current
  end

  def status_indicator
    started? ? "En cours" : "À venir"
  end

  def role
    assignment_role_site&.assignment_role
  end

  def structure_role
    assignment_parent_id.nil? ? 'donneur_ordre' : 'sous_traitant'
  end

  def all_descendants
    assignment_children.flat_map { |child| [child] + child.all_descendants }
  end
end
