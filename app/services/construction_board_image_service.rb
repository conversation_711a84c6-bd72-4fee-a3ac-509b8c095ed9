class ConstructionBoardImageService
  def initialize(construction_board)
    @construction_board = construction_board
  end

  def attach_images(images)
    return false if images.blank?

    begin
      @construction_board.images.attach(images)
      true
    rescue StandardError => e
      Rails.logger.error "Erreur lors de l'upload d'images: #{e.message}"
      false
    end
  end

  def remove_image(image_id)
    image = @construction_board.images.find(image_id)
    image.purge
    true
  rescue ActiveRecord::RecordNotFound
    false
  end

  def generate_qr_code
    require 'rqrcode'

    # Construire l'URL complète pour le QR code
    base_url = ENV['MASHE_BOARDS_URL']
    qr_url = "#{base_url}/boards/#{@construction_board.uuid}"

    qr = RQRCode::QRCode.new(qr_url)
    qr.as_png(
      bit_depth: 1,
      border_modules: 4,
      color_mode: ChunkyPNG::COLOR_GRAYSCALE,
      color: ChunkyPNG::Color.from_hex('#0d0a67'),
      file: nil,
      fill: 'white',
      module_px_size: 20,
      size: 1200
    )
  end

  private

  attr_reader :construction_board
end
