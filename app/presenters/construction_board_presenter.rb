class ConstructionBoardPresenter
  attr_reader :construction_board, :site

  def initialize(construction_board, site)
    @construction_board = construction_board
    @site = site
  end

  # Images methods
  def images_count
    @construction_board.images.count
  end

  def has_images?
    @construction_board.images.attached?
  end

  # Stakeholders methods
  def stakeholders_count
    @site.stakeholders.count
  end

  # Site detail methods
  def site_detail
    @site.site_detail || @site.build_site_detail
  end

  # Site information display methods
  def site_name
    @site.name
  end

  def site_short_name
    @site.short_name
  end

  def site_address
    @site.address
  end

  def company_name
    @site.company.name
  end

  # Site detail display methods
  def floor_area_display
    "#{site_detail.floor_area || 0} m²"
  end

  def permit_number
    site_detail.permit_number
  end

  def formatted_permit_date
    site_detail.permit_date&.strftime("%d/%m/%Y")
  end

  def description
    site_detail.description
  end

  def base_email
    site_detail.base_email
  end

  def has_site_image?
    site_detail.site_image.attached?
  end

  # QR Code methods
  def qr_code_filename
    "qr_code_#{@construction_board.uuid}.png"
  end

  # Helper methods for forms
  def site_detail_exists?
    @site.site_detail.present?
  end

  def formatted_permit_date_for_input
    site_detail.permit_date&.strftime("%Y-%m-%d")
  end

  def site_image_url
    return nil unless has_site_image?
    Rails.application.routes.url_helpers.rails_blob_path(site_detail.site_image, only_path: true)
  end
end
