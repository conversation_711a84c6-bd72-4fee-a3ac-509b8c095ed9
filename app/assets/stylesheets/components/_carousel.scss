.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.relative {
  position: relative;
}

.responsive-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.button {
  border-radius: 50%;
  transition: all 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.1);
}

// Carousel principal container
.carousel-container {
  width: 100%;
  overflow: hidden;
}

.carousel-wrapper {
  position: relative;
  width: 100%;
  height: 400px; // Hauteur fixe du carousel
  background-color: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

// Carousel Bootstrap overrides
#constructionBoardCarousel {
  height: 100%;

  .carousel-inner {
    height: 100%;

    .carousel-item {
      height: 100%;
      position: relative;
      background-color: #f8f9fa;

      // Important: Ne pas utiliser flex ici car cela casse les animations Bootstrap
      // Utiliser plutôt une technique de centrage compatible
      &::before {
        content: '';
        display: inline-block;
        height: 100%;
        vertical-align: middle;
      }

      img {
        display: inline-block;
        vertical-align: middle;
        max-width: 100%;
        max-height: 100%;
        width: auto;
        height: auto;
        object-fit: contain;
      }

      // S'assurer que le texte est centré
      text-align: center;
    }
  }
}

.carousel-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  background-color: rgba($primary-color, 0.5);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  transition: background-color 0.3s ease;
  cursor: pointer;

  &:hover {
    background-color: rgba($primary-color, 0.8);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.25);
  }

  &.left {
    left: 15px;
  }

  &.right {
    right: 15px;
  }

  i {
    font-size: 1.2rem;
  }
}

.btn-danger {
  background-color: rgba($danger-color, 0.1);
  color: $danger-color;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background-color: $danger-color;
    color: white;
    transform: scale(1.05);
  }

  i {
    font-size: 0.875rem;
  }
}

.carousel-actions {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.carousel-item:hover .carousel-actions {
  opacity: 1;
  pointer-events: auto;
}

.empty-carousel {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
  height: 400px; // Même hauteur que le carousel avec images
  background-color: #f8f9fa;
  border-radius: 8px;

  i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #6c757d;
  }

  p {
    margin: 0;
    color: #6c757d;
  }
}

// Responsive adjustments
@media (max-width: 768px) {

  .carousel-wrapper,
  .empty-carousel {
    height: 300px;
  }
}

@media (max-width: 576px) {

  .carousel-wrapper,
  .empty-carousel {
    height: 250px;
  }
}