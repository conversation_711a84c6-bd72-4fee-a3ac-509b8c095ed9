.stakeholders-section {
  width: calc(100vw - 320px);
  overflow-x: hidden;
  margin-right: 0;

  .edit-button-icon {
    font-size: 1.2rem;
    color: $primary-color;
  }

  .stakeholders-scroll {
    display: flex;
    overflow-x: auto;
    padding: 1rem;
    gap: 1rem;
    -webkit-overflow-scrolling: touch;
    width: 100%;

    &::-webkit-scrollbar {
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #888;
    }
  }

  .stakeholder-card {
    flex: 0 0 calc(33.333% - 1rem);
    min-width: 300px;
    max-width: 400px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;

    .edit-button {
      position: absolute;
      top: 10px;
      right: 10px;
      z-index: 1;

      .btn {
        padding: 5px 10px;
        font-size: 0.8rem;
      }
    }

    .stakeholder-content {
      display: flex;
      padding: 1rem;
      gap: 1rem;
    }

    .stakeholder-logo-wrapper {
      flex: 0 0 100px;
    }

    .stakeholder-logo,
    .empty-logo {
      width: 100px;
      height: 100px;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f5f5;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        padding: 8px;
      }

      i {
        font-size: 2rem;
        color: #666;
      }
    }

    .stakeholder-info {
      flex: 1;
      min-width: 0;

      .stakeholder-name {
        font-size: 1.1rem;
        font-weight: 600;
        margin: 0 0 5px 0;
        color: #333;
      }

      .stakeholder-role {
        font-size: 0.9rem;
        color: #666;
        margin: 0 0 15px 0;
      }
    }

    .stakeholder-details {
      .detail-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        i {
          width: 16px;
          color: #666;
          flex-shrink: 0;
        }

        span {
          font-size: 0.85rem;
          color: #333;
          min-width: 0;
        }
      }
    }
  }
}

// Styles pour les formulaires de stakeholders
.stakeholder-form {
  .other-infos-section {
    .other-info-field {
      display: flex;
      align-items: center;

      .col-md-5,
      .col-md-2 {
        display: flex;
        align-items: center;
      }

      .form-control {
        height: 38px;
        border-radius: 8px;
        border: 1px solid #ddd;
        transition: all 0.2s ease;

        &:focus {
          border-color: $primary-color;
          box-shadow: 0 0 0 0.2rem rgba($primary-color, 0.25);
        }
      }

      .delete-stakeholder-field {
        height: 32px;
        width: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        padding: 0;
        text-decoration: none;
        transition: all 0.2s ease;
        border: 1px solid $danger-color;
        background: none;
        color: $danger-color;

        &:hover {
          color: white;
          background: $danger-color;
          transform: translateY(-1px);
        }

        .action-icon {
          font-size: 0.8rem;
        }
      }
    }
  }
}

// set color to primary-color
.add-stakeholder-button {
  color: $primary-color;
  border-radius: 8px;
  padding: 8px 16px;
  text-decoration: none;
  transition: all 0.2s ease-in-out;
  border: 1px solid $primary-color;
  background: none;
  font-weight: 500;

  &:hover {
    color: white;
    background: $primary-color;
    transform: translateY(-1px);
  }

  .action-icon {
    font-size: 0.9rem;
  }
}