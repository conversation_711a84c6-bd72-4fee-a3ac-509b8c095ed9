class ConstructionBoardRepository
  class << self
    def find_or_build_for_site(site)
      site.construction_board || site.build_construction_board
    end

    def with_preloaded_data(construction_board)
      ConstructionBoard.includes(
        :images_attachments,
        site: [
          :stakeholders,
          site_detail: [:site_image_attachment]
        ]
      ).find(construction_board.id)
    end

    def find_with_site_and_stakeholders(site_id)
      site = Site.includes(
        :construction_board,
        :stakeholders,
        site_detail: [:site_image_attachment]
      ).find(site_id)

      construction_board = site.construction_board || site.build_construction_board
      [construction_board, site]
    end
  end
end
